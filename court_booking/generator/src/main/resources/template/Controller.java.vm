package ${package}.modules.${moduleName}.controller;


import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.common.vo.SearchVO;
import com.wteam.framework.common.mybatis.util.PageUtil;
import com.wteam.framework.common.vo.PageVO;
import com.wteam.framework.common.enums.ResultCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;




/**
 * ${comments}
 *
 * <AUTHOR> ${email}
 * @since ${version} ${date}
 */
@RestController
@RequestMapping("${moduleName}/${pathName}")
@Tag(name = "${comments}")
public class ${className}Controller {

    @Autowired
    private ${className}Service ${classname}Service;

    @GetMapping("page")
    @Operation(summary  = "分页")
    public ResultMessage<IPage<${className}>> page(${className} entity,
                                                                SearchVO searchVo,
                                                                PageVO page) {
        IPage<${className}> data = ${classname}Service.page(PageUtil.initPage(page), PageUtil.initWrapper(entity, searchVo));
        return ResultUtil.data(data);

    }

    @GetMapping("{id}")
    @Operation(summary  = "id查询")
    public ResultMessage<${className}> get(@PathVariable("id") Long id) {
        ${className} data = ${classname}Service.getById(id);

        return ResultUtil.data(data);
    }

    @PostMapping
    @Operation(summary  = "保存")
    public ResultMessage save(@RequestBody ${className} entity) {
        //效验数据
        return ${classname}Service.save(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);

    }

    @PutMapping
    @Operation(summary  = "修改")
    public ResultMessage update(@RequestBody ${className} entity) {
        //效验数据
        return ${classname}Service.updateById(entity) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping("/{id}")
    @Operation(summary  = "删除")
    public ResultMessage delete(@PathVariable String id) {
        //效验数据
        return ${classname}Service.removeById(id) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }

    @DeleteMapping
    @Operation(summary  = "批量删除")
    @Parameters({
            @Parameter(name = "ids", description = "ID集合", required = true, schema = @Schema(type = "array",implementation = String.class),  in = ParameterIn.QUERY),
    })
    public ResultMessage delete(@RequestParam List<String> ids) {
        //效验数据
        return ${classname}Service.removeBatchByIds(ids) ? ResultUtil.success() : ResultUtil.error(ResultCode.ERROR);
    }


}