<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.${pk.attrname} ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
#foreach($column in $columns)
  #if($column.columnName != $pk.columnName && $column.columnName != "creator" && $column.columnName != "create_date")
      <el-form-item label="${column.comments}" prop="${column.attrname}">
          <el-input v-model="dataForm.${column.attrname}" placeholder="${column.comments}"></el-input>
      </el-form-item>
  #end
#end
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
#foreach($column in $columns)
        ${column.attrname}: ''#if($velocityCount != $columns.size()),#end

#end
      }
    }
  },
  computed: {
    dataRule () {
      return {
#foreach($column in $columns)
#if($column.columnName != $pk.columnName && $column.columnName != "creator" && $column.columnName != "create_date")
        ${column.attrname}: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]#if($velocityCount != $columns.size()),#end

#end
#end
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.${pk.attrname}) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      #[[this.$http.get(]]#`/${moduleName}/${pathName}/#[[${]]#this.dataForm.${pk.attrname}}`).then(({ data: res }) => {
        if (res.code !== 0) {
          #[[return this.$message.error(res.msg)]]#
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      #[[this.$refs['dataForm'].validate((valid) => {]]#
        if (!valid) {
          return false
        }
        #[[this.$http]]#[!this.dataForm.${pk.attrname} ? 'post' : 'put']('/${moduleName}/${pathName}/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            #[[return this.$message.error(res.msg)]]#
          }
          this.$message({
            #[[message: this.$t('prompt.success'),]]#
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
