-- 菜单初始SQL
INSERT INTO sys_menu(id, pid, name, url, permissions, type, icon, sort, creator, create_date, updater, update_date)VALUES (${id0}, 1067246875800000035, '$!comments', '${moduleName}/${pathName}', NULL, 0, 'icon-desktop', 0, 1067246875800000001, CURRENT_DATE, 1067246875800000001, CURRENT_DATE);
INSERT INTO sys_menu(id, pid, name, url, permissions, type, icon, sort, creator, create_date, updater, update_date) VALUES (${id1}, ${id0}, '查看', NULL, '${moduleName}:${pathName}:page,${moduleName}:${pathName}:info', 1, NULL, 0, 1067246875800000001, CURRENT_DATE, 1067246875800000001, CURRENT_DATE);
INSERT INTO sys_menu(id, pid, name, url, permissions, type, icon, sort, creator, create_date, updater, update_date) VALUES (${id2}, ${id0}, '新增', NULL, '${moduleName}:${pathName}:save', 1, NULL, 1, 1067246875800000001, CURRENT_DATE, 1067246875800000001, CURRENT_DATE);
INSERT INTO sys_menu(id, pid, name, url, permissions, type, icon, sort, creator, create_date, updater, update_date) VALUES (${id3}, ${id0}, '修改', NULL, '${moduleName}:${pathName}:update', 1, NULL, 2, 1067246875800000001, CURRENT_DATE, 1067246875800000001, CURRENT_DATE);
INSERT INTO sys_menu(id, pid, name, url, permissions, type, icon, sort, creator, create_date, updater, update_date) VALUES (${id4}, ${id0}, '删除', NULL, '${moduleName}:${pathName}:delete', 1, NULL, 3, 1067246875800000001, CURRENT_DATE, 1067246875800000001, CURRENT_DATE);
INSERT INTO sys_menu(id, pid, name, url, permissions, type, icon, sort, creator, create_date, updater, update_date) VALUES (${id5}, ${id0}, '导出', NULL, '${moduleName}:${pathName}:export', 1, NULL, 4, 1067246875800000001, CURRENT_DATE, 1067246875800000001, CURRENT_DATE);
