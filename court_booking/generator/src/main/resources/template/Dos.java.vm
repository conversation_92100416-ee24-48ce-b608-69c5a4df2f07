package ${package}.modules.${moduleName}.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


#if(${hasBigDecimal})
import java.math.BigDecimal;
#end
import java.util.Date;

/**
 * ${comments}
 *
 * <AUTHOR> ${email}
 * @since ${version} ${date}
 */
@Data
@TableName("${tableName}")
@Schema(description = "${comments}")
public class ${className} extends BaseEntity {

#foreach ($column in $columns)
	@Schema(description = "$column.comments")
	private $column.attrType $column.attrname;
#end
}