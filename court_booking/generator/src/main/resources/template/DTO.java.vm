package ${package}.modules.${moduleName}.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

#if(${hasBigDecimal})
import java.math.BigDecimal;
#end

/**
 * ${comments}
 *
 * <AUTHOR> ${email}
 * @since ${version} ${date}
 */
@Data
@Schema(value = "${comments}")
public class ${className}DTO implements Serializable {
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)

	@Schema(value = "$column.comments")
	private $column.attrType $column.attrname;

#end

}