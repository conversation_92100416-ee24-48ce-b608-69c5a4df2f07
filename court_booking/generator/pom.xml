<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.wteam.generator</groupId>
    <artifactId>generator</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>generator</name>
    <packaging>jar</packaging>
    <description>generator</description>

    <parent>
        <groupId>com.wteam.spring</groupId>
        <artifactId>spring</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <mybatis.spring.boot.version>2.2.2</mybatis.spring.boot.version>
        <pagehelper.spring.boot.version>1.4.2</pagehelper.spring.boot.version>
        <druid.version>1.2.13</druid.version>
        <commons.lang.version>2.6</commons.lang.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.configuration.version>1.10</commons.configuration.version>
        <velocity.version>1.7</velocity.version>
        <mssql.version>4.0</mssql.version>
        <oracle.version>********</oracle.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis.spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>${pagehelper.spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>${commons.lang.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons.io.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-configuration</groupId>
            <artifactId>commons-configuration</artifactId>
            <version>${commons.configuration.version}</version>
        </dependency>
        <dependency>
            <artifactId>velocity</artifactId>
            <groupId>org.apache.velocity</groupId>
            <version>${velocity.version}</version>
        </dependency>
        <!-- mysql驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <!-- 阿里云maven仓库 -->
    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>


    <!--    <properties>-->
    <!--        <java.version>1.8</java.version>-->
    <!--        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>-->
    <!--        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>-->
    <!--        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>-->
    <!--    </properties>-->

    <!--    <dependencies>-->
    <!--        <dependency>-->
    <!--            <groupId>org.springframework.boot</groupId>-->
    <!--            <artifactId>spring-boot-starter</artifactId>-->
    <!--        </dependency>-->

    <!--        <dependency>-->
    <!--            <groupId>org.springframework.boot</groupId>-->
    <!--            <artifactId>spring-boot-starter-test</artifactId>-->
    <!--            <scope>test</scope>-->
    <!--            <exclusions>-->
    <!--                <exclusion>-->
    <!--                    <groupId>org.junit.vintage</groupId>-->
    <!--                    <artifactId>junit-vintage-engine</artifactId>-->
    <!--                </exclusion>-->
    <!--            </exclusions>-->
    <!--        </dependency>-->
    <!--    </dependencies>-->

    <!--    <dependencyManagement>-->
    <!--        <dependencies>-->
    <!--            <dependency>-->
    <!--                <groupId>org.springframework.boot</groupId>-->
    <!--                <artifactId>spring-boot-dependencies</artifactId>-->
    <!--                <version>${spring-boot.version}</version>-->
    <!--                <type>pom</type>-->
    <!--                <scope>import</scope>-->
    <!--            </dependency>-->
    <!--        </dependencies>-->
    <!--    </dependencyManagement>-->

    <!--    <build>-->
    <!--        <plugins>-->
    <!--            <plugin>-->
    <!--                <groupId>org.apache.maven.plugins</groupId>-->
    <!--                <artifactId>maven-compiler-plugin</artifactId>-->
    <!--                <version>3.8.1</version>-->
    <!--                <configuration>-->
    <!--                    <source>1.8</source>-->
    <!--                    <target>1.8</target>-->
    <!--                    <encoding>UTF-8</encoding>-->
    <!--                </configuration>-->
    <!--            </plugin>-->
    <!--            <plugin>-->
    <!--                <groupId>org.springframework.boot</groupId>-->
    <!--                <artifactId>spring-boot-maven-plugin</artifactId>-->
    <!--                <version>2.3.7.RELEASE</version>-->
    <!--                <configuration>-->
    <!--                    <mainClass>com.wteam.generator.GeneratorApplication</mainClass>-->
    <!--                </configuration>-->
    <!--                <executions>-->
    <!--                    <execution>-->
    <!--                        <id>repackage</id>-->
    <!--                        <goals>-->
    <!--                            <goal>repackage</goal>-->
    <!--                        </goals>-->
    <!--                    </execution>-->
    <!--                </executions>-->
    <!--            </plugin>-->
    <!--        </plugins>-->
    <!--    </build>-->

</project>
