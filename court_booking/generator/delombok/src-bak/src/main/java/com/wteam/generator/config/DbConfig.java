package com.wteam.generator.config;

import com.wteam.generator.dao.GeneratorDao;
import com.wteam.generator.dao.MySQLGeneratorDao;
import com.wteam.generator.utils.RenException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 数据库配置
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
@Configuration
public class DbConfig {
    @Value("${renren.database: mysql}")
    private String database;
    @Autowired
    private MySQLGeneratorDao mySQLGeneratorDao;


    @Bean
    @Primary
    public GeneratorDao getGeneratorDao() {
        if ("mysql".equalsIgnoreCase(database)) {
            return mySQLGeneratorDao;
        } else {
            throw new RenException("不支持当前数据库：" + database);
        }
    }
}
