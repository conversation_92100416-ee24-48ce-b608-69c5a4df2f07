package com.wteam.framework.modules.connect.config;

import com.wteam.framework.modules.connect.entity.dto.AuthCallback;
import com.wteam.framework.modules.connect.request.BaseAuthRequest;
import com.xkcoding.http.config.HttpConfig;
import lombok.*;

import java.util.List;

/**
 * JustAuth配置类
 *
 * <AUTHOR> (yadong.zhang0415(a)gmail.com)
 * @since 1.8
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthConfig {

    /**
     * 客户端id：对应各平台的appKey
     */
    private String clientId;

    /**
     * 客户端Secret：对应各平台的appSecret
     */
    private String clientSecret;

    /**
     * 登录成功后的回调地址
     */
    private String redirectUri;

    /**
     * 支付宝公钥：当选择支付宝登录时，该值可用
     * 对应“RSA2(SHA256)密钥”中的“支付宝公钥”
     */
    private String alipayPublicKey;

    /**
     * 是否需要申请unionid，目前只针对qq登录
     * 注：qq授权登录时，获取unionid需要单独发送邮件申请权限。如果个人开发者账号中申请了该权限，可以将该值置为true，在获取openId时就会同步获取unionId
     * 参考链接：http://wiki.connect.qq.com/unionid%E4%BB%8B%E7%BB%8D
     * <p>
     * 1.7.1版本新增参数
     */
    private boolean unionId;

    /**
     * Stack Overflow Key
     * <p>
     *
     * @since 1.9.0
     */
    private String stackOverflowKey;

    /**
     * 企业微信，授权方的网页应用ID
     *
     * @since 1.10.0
     */
    private String agentId;

    /**
     * 使用 Coding 登录时，需要传该值。
     * <p>
     * 团队域名前缀，比如以“ https://justauth.coding.net/ ”为例，{@code codingGroupName} = justauth
     *
     * @since 1.15.5
     */
    private String codingGroupName;

    /**
     * 针对国外服务可以单独设置代理
     * HttpConfig config = new HttpConfig();
     * <<<<<<< HEAD
     * config.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("***************", 10080)));
     * =======
     * config.setProxy(new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 10080)));
     * >>>>>>> ae0c4aea12996d3d72eca7c6ccdc97922373e4d7
     * config.setTimeout(15000);
     *
     * @since 1.15.5
     */
    private HttpConfig httpConfig;

    /**
     * 忽略校验 {@code state} 参数，默认不开启。当 {@code ignoreCheckState} 为 {@code true} 时，
     * {@link BaseAuthRequest#login(AuthCallback)}} 将不会校验 {@code state} 的合法性。
     * <p>
     * 使用场景：当且仅当使用自实现 {@code state} 校验逻辑时开启
     * <p>
     * 以下场景使用方案仅作参考：
     * 1. 授权、登录为同端，并且全部使用 JustAuth 实现时，该值建议设为 {@code false};
     * 2. 授权和登录为不同端实现时，比如前端页面拼装 {@code authorizeUrl}，并且前端自行对{@code state}进行校验，
     * 后端只负责使用{@code code}获取用户信息时，该值建议设为 {@code true};
     *
     * <strong>如非特殊需要，不建议开启这个配置</strong>
     * <p>
     * 该方案主要为了解决以下类似场景的问题：
     *
     * @see <a href="https://github.com/justauth/JustAuth/issues/83">https://github.com/justauth/JustAuth/issues/83</a>
     * @since 1.15.6
     */
    private boolean ignoreCheckState;

    /**
     * 支持自定义授权平台的 scope 内容
     *
     * @since 1.15.7
     */
    private List<String> scopes;
}
