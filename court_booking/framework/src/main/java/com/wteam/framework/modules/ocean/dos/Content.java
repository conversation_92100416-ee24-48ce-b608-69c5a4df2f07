package com.wteam.framework.modules.ocean.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@Data
@TableName("content")
@Schema(description = "")
public class Content extends BaseEntity {

	@Schema(description = "")
	private String id;
	@Schema(description = "内容")
	private String content;
}