package com.wteam.framework.modules.ocean.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 电子书
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@Data
@TableName("ebook")
@Schema(description = "电子书")
public class Ebook extends BaseEntity {

	@Schema(description = "id")
	private String id;
	@Schema(description = "名称")
	private String name;
	@Schema(description = "分类1")
	private String category1Id;
	@Schema(description = "分类2")
	private String category2Id;
	@Schema(description = "描述")
	private String description;
	@Schema(description = "封面")
	private String cover;
	@Schema(description = "文档数")
	private Integer docCount;
	@Schema(description = "阅读数")
	private Integer viewCount;
	@Schema(description = "点赞数")
	private Integer voteCount;
}