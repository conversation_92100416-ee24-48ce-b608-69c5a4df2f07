package com.wteam.framework.modules.pay.entity.enums;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @since 2020/12/18 18:08
 */
public enum PaymentMethodEnum {

    /**
     * 微信
     */
    WECHAT("wxV3PayPlugin", "微信", "NATIVE"),
    /**
     * 支付宝
     */
    ALIPAY("aliPayPlugin", "支付宝", "PAGE"),


    /**
     * 银联-云闪付
     */
    UNIONPAY("unionPayPlugin", "银联", "paymentMethod");

    /**
     * 插件id 调用对象，需要实现payment接口
     */
    private final String plugin;
    /**
     * 支付名称
     */
    private final String paymentName;

    /**
     * 支付方式
     */
    private final String paymentMethod;

    public String getPlugin() {
        return plugin;
    }

    public String paymentName() {
        return paymentName;
    }

    public String paymentMethod() {
        return paymentMethod;
    }

    /**
     * 根据支付方式名称返回对象
     *
     * @param name
     * @return
     */
    public static PaymentMethodEnum paymentNameOf(String name) {
        for (PaymentMethodEnum value : PaymentMethodEnum.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    PaymentMethodEnum(String plugin, String paymentName, String paymentMethod) {
        this.plugin = plugin;
        this.paymentName = paymentName;
        this.paymentMethod = paymentMethod;
    }

}
