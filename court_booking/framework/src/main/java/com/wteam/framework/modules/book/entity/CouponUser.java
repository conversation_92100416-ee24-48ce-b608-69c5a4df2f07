package com.wteam.framework.modules.book.entity;


import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 优惠券用户中间表(CouponUser)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:38:29
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_coupon_user")
public class CouponUser {
    //id@TableId
    private Long id;

    //创建者
    private String createBy;
    //创建时间
    private String createTime;
    //删除标志 true/false 删除/未删除
    private Boolean deleteFlag;
    //更新者
    private String updateBy;
    //更新时间
    private String updateTime;
    //谁持有优惠券
    private Long userId;
    //优惠券id
    private Long couponId;
    //是否可用
    private Integer effective;
    //优惠券说明
    private String illustrate;


}
