package com.wteam.framework.modules.ocean.dos.enums;// package com.quanxiaoha.weblog.admin.enums;
//
// import com.quanxiaoha.weblog.common.exception.BaseExceptionInterface;
// import lombok.AllArgsConstructor;
// import lombok.Getter;
//
// /**
//  * @author: 犬小哈
//  * @url: www.quanxiaoha.com
//  * @date: 2023-04-18 8:14
//  * @description: 响应枚举
//  **/
// @Getter
// @AllArgsConstructor
// public enum AdminResponseCodeEnum implements BaseExceptionInterface {
//
//     // SYSTEM_ERROR("10000", "出错啦，后台小哥正在努力修复中");
//
//     private String errorCode;
//     private String errorMessage;
//
// }
