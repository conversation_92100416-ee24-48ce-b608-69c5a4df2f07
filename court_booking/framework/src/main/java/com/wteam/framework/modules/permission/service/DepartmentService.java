package com.wteam.framework.modules.permission.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wteam.framework.modules.permission.entity.dos.Department;
import com.wteam.framework.modules.permission.entity.vo.DepartmentVO;

import java.util.List;

/**
 * 部门业务层
 *
 * <AUTHOR>
 * @since 2020/11/17 3:43 下午
 */
public interface DepartmentService extends IService<Department> {

    /**
     * 获取部门树
     *
     * @param initWrapper
     * @return
     */
    List<DepartmentVO> tree(QueryWrapper<Department> initWrapper);

    /**
     * 删除部门
     *
     * @param ids
     */
    void deleteByIds(List<String> ids);
}