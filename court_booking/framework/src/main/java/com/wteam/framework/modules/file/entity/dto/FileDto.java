package com.wteam.framework.modules.file.entity.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 文件系统
 *
 * <AUTHOR>
 * @since 2020/11/26 15:35
 */
@Data
@TableName("dp_file")
@Schema(description = "文件")
public class FileDto {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
//    @Schema(value = "唯一标识", hidden = true)
    private String id;


    @CreatedBy
    @TableField(fill = FieldFill.INSERT)
//    @Schema(value = "创建者", hidden = true)
    private String createBy;

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
//    @Schema(value = "创建时间", hidden = true)
    private Date createTime;

    @LastModifiedBy
    @TableField(fill = FieldFill.UPDATE)
//    @Schema(value = "更新者", hidden = true)
    private String updateBy;

    @LastModifiedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
//    @Schema(value = "更新时间", hidden = true)
    private Date updateTime;

    @TableField(fill = FieldFill.INSERT)
//    @Schema(value = "删除标志 true/false 删除/未删除", hidden = true)
    private Boolean deleteFlag;


    @Schema(description = "原文件名")
    private String name;

    @Schema(description = "存储文件名")
    private String fileKey;

    @Schema(description = "大小")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long fileSize;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "路径")
    private String url;

    @Schema(description = "拥有者id")
    private String ownerId;

    @Schema(description = "拥有者用户名")
    private String ownerName;


    private String enId;

    private String state;

    private String opened;

    private String status;


    private String description;


    private String parentId;

//    private String enId;

}