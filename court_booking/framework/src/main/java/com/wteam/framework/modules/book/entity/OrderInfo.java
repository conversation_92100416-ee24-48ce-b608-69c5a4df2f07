package com.wteam.framework.modules.book.entity;

import java.util.Date;

import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * (OrderInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:38:46
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_order_info")
public class OrderInfo {

    //    @JsonSerialize(using = LongJsonSerializer.class)
//    @JsonDeserialize(using = LongJsonDeserializer.class)
    @TableId
    private String id;

    //创建者
    private String createBy;
    //创建时间
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    //修改者
    private String updateBy;
    //修改时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //用户id
    private String userId;
    //门店id
    private Long storeId;
    //预约日期
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date orderDate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "预约开始时间")
    private Date orderSt;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "预约结束时间")
    private Date orderEd;
    //订单状态(枚举）
    private String orderState;
    //订单价格
    private Double orderPrice;
    //订单类型（枚举）
    private String orderType;
    //支付方式
    private String paymentMethods;
    //分享次数
    private Integer share;
    //场地id
    private Long venueId;
    //手机号码
    private String phoneNumber;
    //二维码
    private String qrCode;
    //用户进场时间
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "用户进场时间")
    private Date startTime;
    //用户离开时间
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "用户离场时间")
    private Date endTime;
    //购买卡的id
    private Integer cardId;
    //不知名id
    private String transactionId;
    //订单超时金额
    private Double overPrice;
    //订单超时时间
    private Integer overTime;
    //计时码超时金额订单id
    private Integer overId;
    //超一个小时的时间戳
    private Integer overTimestamp;
    //结束计时的时间戳
    private Integer timeoutStamp;
}
