//package com.wteam.framework.modules.hardware;
//
///**
// * <AUTHOR>
// * @date 2023/9/29 15:42
// */
//
//
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import cn.hutool.http.HttpUtil;
//import cn.hutool.json.JSONObject;
//import com.wteam.framework.common.enums.ResultUtil;
//import com.wteam.framework.common.exception.ServiceException;
//import com.wteam.framework.modules.book.entity.Card;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.logging.Log;
//
//import java.time.LocalDate;
//import java.time.ZoneOffset;
//
//@Slf4j
//public class ApiQrCodeUtil {
//    private static final String API_URL = "https://xiaofeng.pankzone.com/api/openapi/reg_access_qcode.x";
//    private static final String MAC_ADDRESS = "863569068849199";
//
//    // 这个方法获取token，你需要根据你的实际需求实现它
//    public static String getToken(String mac) {
//        // 实现获取token的逻辑
//        String token = ApiCallerUtil.callApi(mac);
//        return token;
//    }
//
//    public static Boolean callApiWithOrderInfo(Card card, String data) {
//        //获得token
//        String tk = getToken();
//        //获取二维码有效时间
//        Integer days = card.getDays();
//        // 计算二维码的过期时间
//        LocalDate baseDate = LocalDate.now();
//        LocalDate targetDate = baseDate.plusDays(days);
//        long timestamp = targetDate.atStartOfDay().toInstant(ZoneOffset.UTC).toEpochMilli();
//        // 使用hutool调用接口
//        HttpResponse response = HttpRequest.post(API_URL)
//                .form("mac", MAC_ADDRESS)
//                .form("tk", tk)
//                .form("qcodetxt", data)
//                .form("enabletime", timestamp)
//                .form("enablecount", 6000)
//                .execute();
//        // 获取响应结果
//        String responseBody = response.body();
//        int status = response.getStatus();
//
//        // 处理响应
//        if (status == 200) {
//            // 请求成功，可以处理响应数据
//            JSONObject jsonObject = new JSONObject(responseBody);
//            int code = jsonObject.getInt("code");
//            String desc = jsonObject.getStr("desc");
//            log.info("此时的code为：{}", code);
//            log.info("此时的desc为：{}", desc);
//            return true;
//        }
//        return false;
//    }
//
//    public static Boolean callApiWithUser(String data) {
//        //获得token
//        String tk = getToken();
//        // 使用hutool调用接口
//        String baseUrl = "https://xiaofeng.pankzone.com/api/openapi/reg_access_qcode.x";
//        String mac = "863569068849199";
//        String qcodetxt = data;
//        int enablecount = 2;
//        // 使用StringBuilder拼接URL
//        StringBuilder urlBuilder = new StringBuilder(baseUrl);
//        urlBuilder.append("?mac=").append(mac)
//                .append("&tk=").append(tk)
//                .append("&qcodetxt=").append(qcodetxt)
//                .append("&enablecount=").append(enablecount);
//        String finalUrl = urlBuilder.toString();
//        // 发送HTTP GET 请求
//        HttpResponse response = HttpUtil.createGet(finalUrl)
//                .execute();
//        // 获取响应状态码
//        int statusCode = response.getStatus();
//        // 获取响应内容
//        String responseBody = response.body();
//        // 处理响应
//        if (statusCode == 200) {
//            // 请求成功，可以处理响应数据
//            JSONObject jsonObject = new JSONObject(responseBody);
//            int code = jsonObject.getInt("code");
//            String desc = jsonObject.getStr("desc");
//            log.info("此时的code为：{}", code);
//            log.info("此时的desc为：{}", desc);
//            return true;
//        }
//        return false;
//    }
//}
//
//
//
