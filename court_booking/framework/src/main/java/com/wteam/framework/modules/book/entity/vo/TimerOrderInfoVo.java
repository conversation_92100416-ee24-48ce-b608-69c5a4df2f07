package com.wteam.framework.modules.book.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/14 13:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimerOrderInfoVo {
    @TableId
    private String id;
    //订单价格
    private Double orderPrice;
    //订单状态(枚举）
    private String orderState;
    //订单类型（枚举）
    private String orderType;
    //用户进场时间
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "用户进场时间")
    private Date startTime;
    //用户离开时间
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "用户离场时间")
    private Date endTime;
    //订单超时金额
    private Double overPrice;
    private Integer overId;

    //超一个小时的时间戳
    private Integer overTimestamp;
    //结束计时的时间戳
    private Integer timeoutStamp;

}
