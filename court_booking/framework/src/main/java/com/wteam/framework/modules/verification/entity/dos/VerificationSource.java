package com.wteam.framework.modules.verification.entity.dos;


import com.baomidou.mybatisplus.annotation.TableName;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 验证码资源维护
 *
 * <AUTHOR>
 * @since 2021/1/30 4:13 下午
 */
@Data
@TableName("li_verification_source")
@Schema(description = "验证码资源维护")
public class VerificationSource extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "资源地址")
    private String resource;

    /**
     * @see
     */
    @Schema(description = "验证码资源类型 SLIDER/SOURCE")
    private String type;
}