package com.wteam.framework.modules.book.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/9/18 22:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StoreForm {
    private Long id;
    //卡名称
    private String cardName;
    //卡类型（储值卡、月卡、次卡枚举）
    private String cardType;
    //有效期
    private Integer days;
    //生效日期
    private String startDate;
    //结束日期
    private String endDate;
    //面值
    private Integer faceValue;
    //赠送金额
    private Integer bonusAmount;
}
