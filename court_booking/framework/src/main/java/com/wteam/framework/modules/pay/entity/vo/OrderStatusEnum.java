package com.wteam.framework.modules.pay.entity.vo;

/**
 * <AUTHOR> @ qq.com)
 * @date 2022/8/2 5:00 PM
 */
/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @since 2020/11/17 7:27 下午
 */
public enum OrderStatusEnum {

    /**
     * 订单状态
     */
    UNPAID("未付款"),
    PAID("已付款");


    private final String description;

    OrderStatusEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String description() {
        return this.description;
    }


}
