package com.wteam.framework.modules.book.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Time;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @date 2023/9/19 22:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StoreVo {
    //门店名称
    private String storeName;
    //地址
    private String address;
    //场馆介绍
    private String venueIntroduction;
    //开店时间
    private String openingTime;
    //关店时间
    private String closingTime;
    //服务热线
    private String serviceHotline;
    //微信号
    private String wxCode;
    //实时人数
    private String realTime;
    //提前预订天数
    private String advanceDays;
    //起订时间
    private Integer leadTime;
    //公告
    private String announcement;
    //订场须知
    private String scheduledNotice;
}
