package com.wteam.framework.common.security.enums;

/**
 * token角色类型
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2020/8/18 15:23
 */
public enum UserEnums {
    /**
     * 角色
     */
    MEMBER("会员"),
    MANAGER("管理员"),
    SYSTEM("系统"),
    USER("普通用户");
    private final String role;

    UserEnums(String role) {
        this.role = role;
    }

    public String getRole() {
        return role;
    }
}
