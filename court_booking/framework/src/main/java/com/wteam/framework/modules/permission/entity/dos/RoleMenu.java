package com.wteam.framework.modules.permission.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 角色权限绑定关系
 *
 * <AUTHOR>
 * @since 2020/11/19 12:18
 */
@Data
@TableName("li_role_menu")
@Schema(description = "角色权限")
public class RoleMenu extends BaseEntity {

    private static final long serialVersionUID = -4680260092546996026L;

    @Schema(description = "角色id")
    private String roleId;

    @Schema(description = "菜单")
    private String menuId;

    @Schema(description = "是否拥有操作数据权限，为否则只有查看权限")
    private Boolean isSuper;

}