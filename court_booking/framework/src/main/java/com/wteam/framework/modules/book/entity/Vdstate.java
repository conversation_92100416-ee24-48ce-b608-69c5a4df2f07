package com.wteam.framework.modules.book.entity;

import java.util.Date;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (Vdstate)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:40:04
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_vdstate")
public class Vdstate {
    //id@TableId
    private Integer id;

    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //修改者
    private String updateBy;
    //修改时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //门店id
    private Long storeId;
    //日期
    private Date date;
    //分时状态 第i位代表第i~i+1小时的场馆状态0代表不可用1代表可用2代表已经被预约
    private String vdstateSt;


}
