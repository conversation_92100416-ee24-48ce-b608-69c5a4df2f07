package com.wteam.framework.modules.oss.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

public interface OssService {

    /**
     *返回上传文件的url地址
     *数据流提交
     * 如果是浏览器端上传,最后会拿到一个inputstream的
     * @return
     */
    /**
     * 阿里云oss文件上传
     *
     * @param inputStream      输入流
     * @param moudle           文件夹名称,上传到那个目录
     * @param originalFileName 原始文件名
     * @return 文件图片在oss服务器上的url
     */
    String upload(InputStream inputStream, String moudle, String originalFileName);

    /**
     * 根据原来上传上去的文件名字删除文件/照片
     *
     * @param objectName 文件名
     * @return 删除成功
     */
    String delete(String objectName);

    List<String> upFiles(String moudle, MultipartFile[] multipartFiles);
}
