package com.wteam.framework.modules.book.entity;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (CbAdminUser)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:36:39
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_admin_user")
public class AdminUser {

    //ID
    private Long id;
    //创建者
    private String createBy;
    //创建时间
    private String createTime;
    //删除标志 true/false 删除/未删除
    private Boolean deleteFlag;
    //更新者
    private String updateBy;
    //更新时间
    private String updateTime;
    //用户头像
    private String avatar;
    //备注
    private String description;
    //邮件
    private String email;
    //是否是超级管理员 超级管理员/普通管理员
    private Boolean isSuper;
    //手机
    private String mobile;
    //昵称
    private String nickName;
    //密码
    private String password;
    //状态 默认true正常 false禁用
    private Boolean status;
    //用户名
    private String username;
    //角色ID集合
    private String roleIds;
    //是否被禁用
    private Boolean isBan;


}
