package com.wteam.framework.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * (User)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-14 19:19:45
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_user")
@ApiModel(description = "用户实体类")
public class User {

    @TableId
    @NotNull
    @ApiModelProperty(value = "id")
    private String id;
    //创建者
    @ApiModelProperty(value = "创建者")
    private String createBy;
    //创建时间
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    //修改者
    private String updateBy;
    //修改时间
    private String updateTime;
    //删除标志 true/false 删除/未删除
    private Boolean deleteFlag;
    //微信openid
    private String openId;
    //头像
    private String avatar;
    //昵称（默认用户+手机号后四位）
    private String nickName;
    //手机号
    private String phoneNumber;
    //生日
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Schema(description = "生日")
    private Date birthday;
    //性别(0表示男，1表示女）
    private Integer sex;
    //邮件
    private String email;
    //积分
    private Double credit;
    //姓名
    private String name;
    //状态 默认true正常 false禁用
    private Boolean status;
}
