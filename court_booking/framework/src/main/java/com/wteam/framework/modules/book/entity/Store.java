package com.wteam.framework.modules.book.entity;

import java.time.LocalTime;
import java.util.Date;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
/**
 * (Store)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-02 17:45:13
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_store")
public class Store  {
    //id@TableId
    private Long id;

    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //修改者
    private String updateBy;
    //修改时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //门店名称
    private String storeName;
    //场馆介绍
    private String venueIntroduction;
    //开店时间
    private String openingTime;
    //关店时间
    private String closingTime;
    //门店图片
    private String storeImages;
    //服务热线
    private String serviceHotline;
    //微信号
    private String wxCode;
    //实时人数
    private String realTime;
    //提前预订天数
    private String advanceDays;
    //起订时间
    private Integer leadTime;
    //公告
    private String announcement;
    //门店地址
    private String address;
    //订场须知
    private String scheduledNotice;



}
