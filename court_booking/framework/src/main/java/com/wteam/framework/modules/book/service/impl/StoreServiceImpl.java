package com.wteam.framework.modules.book.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.exception.ServiceException;
import com.wteam.framework.common.utils.BeanUtil;
import com.wteam.framework.common.vo.ResultMessage;

import com.wteam.framework.modules.book.entity.Store;
import com.wteam.framework.modules.book.entity.Venue;
import com.wteam.framework.modules.book.entity.vo.StoreDetailsVo;
import com.wteam.framework.modules.book.entity.vo.StoreVo;
import com.wteam.framework.modules.book.mapper.StoreMapper;
import com.wteam.framework.modules.book.mapper.VenueMapper;
import com.wteam.framework.modules.book.service.StoreService;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;


import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * (Store)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-16 22:56:04
 */
@Slf4j
@Service("storeService")
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store> implements StoreService {


    @Autowired
    private StoreMapper storeMapper;
    @Autowired
    private VenueMapper venueMapper;


    @Override
    public ResultMessage<List<String>> getPhoto(@NotNull Long id) {
        // 校验参数信息
        Store store = storeMapper.selectById(id);
        if (ObjectUtil.isEmpty(store)) {
            return ResultUtil.error(ResultCode.ENTITY_NULL);
        }
        // 创建一个List来存储URL值
        List<String> imageUrlList = new ArrayList<>();
        try {
            // 解析存储图片信息的 JSON 数据
            String storeImagesJson = store.getStoreImages();
            JSONObject imagesJson = new JSONObject(storeImagesJson);
            //获取"images"数组
            JSONArray imagesArray = imagesJson.getJSONArray("images");
            // 遍历数组并获取每个URL值
            for (Object obj : imagesArray) {
                String imageUrl = obj.toString();
                imageUrlList.add(imageUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResultUtil.data(imageUrlList);
    }


    @Override
    public ResultMessage getDetails(@NotNull Long id) {
        try {
            Store store = Optional.ofNullable(storeMapper.selectById(id))
                    .orElseThrow(() -> new ServiceException(ResultCode.ENTITY_NULL));
            List<Venue> venues = Optional.ofNullable(venueMapper.selectList(new LambdaQueryWrapper<Venue>().eq(Venue::getStoreId, id)))
                    .orElseThrow(() -> new ServiceException(ResultCode.ENTITY_NULL));
            StoreVo storeVo = new StoreVo();
            BeanUtil.copyProperties(store, storeVo);
            log.info("此时的门店信息为：{}", storeVo);
            StoreDetailsVo storeDetailsVo = new StoreDetailsVo(storeVo, venues);
            return ResultUtil.data(storeDetailsVo);
        } catch (ServiceException ce) {
            log.error("获取门店信息时出现异常: {}", ce.getMessage(), ce);
            return ResultUtil.error(ce.getResultCode());
        } catch (Exception e) {
            log.error("获取门店信息时出现异常: {}", e.getMessage(), e);
            return ResultUtil.error(ResultCode.INTERNAL_ERROR);
        }
    }
}

//    @Override
//    public ResultMessage getAnnouncement(@NotNull Long id) {
//        try {
//            Store store = storeMapper.selectById(id);
//            if (ObjectUtil.isEmpty(store)) {
//                return ResultUtil.error(ResultCode.ENTITY_NULL);
//            }
//            String announcement = store.getAnnouncement();
//            return ResultUtil.data(announcement);
//        } catch (Exception e) {
//            // 处理可能的异常情况，例如数据库查询失败
//            log.error("获取门店公告时出现异常: {}", e.getMessage(), e);
//            return ResultUtil.error(ResultCode.INTERNAL_ERROR);
//        }
//    }

