package com.wteam.framework.modules.book.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wteam.framework.common.enums.orderTypeEnum;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.book.entity.OrderInfo;
import com.wteam.framework.modules.book.entity.dto.OrderReq;
import com.wteam.framework.modules.pay.entity.vo.CallbackParamVO;


import java.util.Date;


/**
 * (OrderInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-20 13:12:50
 */
public interface OrderInfoService extends IService<OrderInfo> {


    ResultMessage generateOrder(String accessToken, OrderReq orderReq);

    Boolean checkOrder(Long id);

    void processOrders(CallbackParamVO callbackParamVO);


    ResultMessage bookStatus(Long id, Date dateTime);

    ResultMessage getTimer(String userId);
}
