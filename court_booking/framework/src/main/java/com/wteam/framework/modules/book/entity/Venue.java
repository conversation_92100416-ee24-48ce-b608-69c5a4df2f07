package com.wteam.framework.modules.book.entity;

import java.util.Date;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (Venue)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-02 17:45:53
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_venue")
public class Venue {
    //id@TableId
    private Integer id;
    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //修改者
    private String updateBy;
    //修改时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //场地名称
    private String venueName;
    //门店id
    private Long storeId;
    //上午时间段半场价格
    private Double amHalfPrice;
    //下午篮球半场价格
    private Double pmHalfPrice;
    //上午篮球场，羽毛球场，乒乓球全场价格
    private Double amAllPrice;
    //下午篮球场全场，羽毛球，乒乓球场价格
    private Double pmAllPrice;
    //场地关联id
    private String relevancyId;
    //场地购买须知
    private String purchaseInstructions;
    //特殊价格
    private String specialValue;
    //场地类型0为篮球场，1为羽毛球场，2为乒乓球场
    private String venueType;
    //TODO:新添加的
    //场地起订时间
    private Double leadTime;
    @Schema(description = "场地分享次数")
    private Integer shareFrequency;


}
