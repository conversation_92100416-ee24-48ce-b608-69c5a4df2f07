package com.wteam.framework.modules.user.token;

import com.wteam.framework.common.security.AuthUser;
import com.wteam.framework.common.security.enums.UserEnums;
import com.wteam.framework.common.security.token.Token;
import com.wteam.framework.common.security.token.TokenUtils;
import com.wteam.framework.common.security.token.base.AbstractTokenGenerate;
import com.wteam.framework.modules.permission.entity.dos.AdminUser;
import com.wteam.framework.modules.user.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 用户token生成
 *
 * <AUTHOR>
 * @version v4.0
 */
@Component
public class UserTokenGenerate extends AbstractTokenGenerate<User> {

    @Autowired
    private TokenUtils tokenUtil;

    /**
     * 生成登录token
     *
     * @param user     用户名
     * @param longTerm 是否长时间有效
     * @return
     */

    @Override
    public Token createToken(User user, Boolean longTerm) {
        AuthUser authUser = new AuthUser(user.getOpenId(), user.getId().toString(), user.getNickName(), user.getNickName(), UserEnums.USER);
        //登录成功就生成token
        return tokenUtil.createToken(user.getNickName(), authUser, longTerm, UserEnums.USER);
    }

    /**
     * 刷新token
     *
     * @param refreshToken 刷新token
     * @return
     */
    @Override
    public Token refreshToken(String refreshToken) {
        return tokenUtil.refreshToken(refreshToken, UserEnums.USER);
    }


    //    @Override
//    public Token createToken(User user, Boolean longTerm) {


//        ClientTypeEnum clientTypeEnum;
//
//        try {
//            //获取客户端类型
//            String clientType = ThreadContextHolder.getHttpRequest().getHeader("clientType");
//            //如果客户端为空，则缺省值为PC，pc第三方登录时不会传递此参数
//            if (clientType == null) {
//                clientTypeEnum = ClientTypeEnum.PC;
//            } else {
//                clientTypeEnum = ClientTypeEnum.valueOf(clientType);
//            }
//        } catch (Exception e) {
//            clientTypeEnum = ClientTypeEnum.UNKNOWN;
//        }
//        //记录最后登录时间，客户端类型
//        user.setLastLoginDate(new Date());
//        user.setClientEnum(clientTypeEnum.name());
//       String destination = rocketmqCustomProperties.getMemberTopic() + ":" + MemberTagsEnum.MEMBER_LOGIN.name();
//       rocketMQTemplate.asyncSend(destination, member, RocketmqSendCallbackBuilder.commonCallback());
//
//        AuthUser authUser = new AuthUser(member.getUsername(), member.getId(), member.getNickName(), member.getFace(), UserEnums.MEMBER);
//        //登陆成功生成token
//        return tokenUtil.createToken(member.getUsername(), authUser, longTerm, UserEnums.MEMBER);
//    }
//    }
}
