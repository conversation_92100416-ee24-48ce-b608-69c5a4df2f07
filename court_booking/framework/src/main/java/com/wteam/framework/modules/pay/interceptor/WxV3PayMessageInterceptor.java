
package com.wteam.framework.modules.pay.interceptor;

import com.egzosn.pay.common.api.PayMessageHandler;
import com.egzosn.pay.common.api.PayMessageInterceptor;
import com.egzosn.pay.common.exception.PayErrorException;
import com.egzosn.pay.wx.v3.api.WxPayService;
import com.egzosn.pay.wx.v3.bean.response.WxPayMessage;

import java.util.Map;

/**
 * 支付宝回调信息拦截器
 *
 * <AUTHOR>
 * email <EMAIL>
 * date 2017/1/18 19:28
 */
public class WxV3PayMessageInterceptor implements PayMessageInterceptor<WxPayMessage, WxPayService> {


    /**
     * 拦截支付消息
     *
     * @param payMessage 支付回调消息
     * @param context    上下文，如果handler或interceptor之间有信息要传递，可以用这个
     * @param payService 支付服务
     * @return true代表OK，false代表不OK并直接中断对应的支付处理器
     * @throws PayErrorException PayErrorException*
     * @see PayMessageHandler 支付处理器
     */
    @Override
    public boolean intercept(WxPayMessage payMessage, Map<String, Object> context, WxPayService payService) throws PayErrorException {
        //这里进行拦截器处理，自行实现
//        String outTradeNo = payMessage.getOutTradeNo();
//        OrderService orderService = (OrderService) SpringContextUtil.getBean(OrderService.class);
//        // 设置外部单号
//        return orderService.checkOrder(outTradeNo);
        return true;
    }
}
