package com.wteam.framework.modules.book.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.book.entity.Store;


/**
 * (Store)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-16 22:56:03
 */
public interface StoreService extends IService<Store> {

    ResultMessage getPhoto(Long id);


    ResultMessage getDetails(Long id);
}
