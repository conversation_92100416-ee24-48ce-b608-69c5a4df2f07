package com.wteam.framework.modules.book.entity;


import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (CbAddress)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:29:36
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_address")
@ApiModel(description = "地址信息")
public class Address {
    @TableId
    private Long addressId;
    //删除标志 true/false 删除/未删除
    private Boolean deleteFlag;
    //创建者
    private String createBy;
    //创建时间
    private String createTime;
    //修改者
    private String updateBy;
    //修改时间
    private String updateTime;
    //用户id
    private String userId;
    //收货人的姓名
    private String recipientName;
    //街道地址
    private String streetAddress;
    //城市
    private String city;
    //州/省份
    private String stateProvince;
    //手机号码
    private String phoneNumber;
    //是否为默认地址
    private Integer isDefault;
}
