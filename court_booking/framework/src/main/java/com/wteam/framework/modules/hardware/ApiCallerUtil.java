package com.wteam.framework.modules.hardware;


import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import com.wteam.framework.common.exception.ServiceException;

/**
 * 固定方法用来获取token
 */
public class ApiCallerUtil {

    public static String callApi(String mac) {

        // 固定的接口URL
        String apiUrl = "https://xiaofeng.pankzone.com/api/openapi/get_tokenv2.x";

        // 固定的参数
        String pwd = "239818";
        String apikey = "889a7a889678edfef18fa669def89";

        // 构建请求参数
        String urlWithParams = apiUrl + "?mac=" + mac + "&pwd=" + pwd + "&apikey=" + apikey;

        // 发起GET请求并获取响应
        String response = HttpUtil.get(urlWithParams);
        // 解析JSON响应
        JSONObject json = new JSONObject(response);
        // 获取code和token的值
        int code = json.getInt("code");
        String token = json.getStr("token");
        if (code == 0) {
            return token;
        } else {
            throw new ServiceException("请求接口有误！");
        }
    }
}
