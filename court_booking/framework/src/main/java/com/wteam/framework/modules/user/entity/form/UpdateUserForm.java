package com.wteam.framework.modules.user.entity.form;

import com.wteam.framework.modules.user.entity.User;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/9/16 22:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateUserForm extends User {
    private String avatar;
    private String nickName;
    private Date birthday;
    private String name;
    private Integer sex;
    private String email;
}
