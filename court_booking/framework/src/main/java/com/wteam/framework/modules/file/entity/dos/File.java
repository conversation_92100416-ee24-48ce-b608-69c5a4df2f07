package com.wteam.framework.modules.file.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 文件系统
 *
 * <AUTHOR>
 * @since 2020/11/26 15:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_file")
@Schema(description = "文件")
public class File extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "原文件名")
    private String name;

    @Schema(description = "存储文件名")
    private String fileKey;

    @Schema(description = "大小")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long fileSize;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "路径")
    private String url;

    @Schema(description = "拥有者id")
    private String ownerId;

    @Schema(description = "用户类型")
    private String userEnums;
}