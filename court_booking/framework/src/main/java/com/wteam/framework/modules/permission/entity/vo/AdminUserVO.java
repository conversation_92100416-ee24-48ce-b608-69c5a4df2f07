package com.wteam.framework.modules.permission.entity.vo;


import com.wteam.framework.common.utils.BeanUtil;
import com.wteam.framework.modules.permission.entity.dos.AdminUser;
import com.wteam.framework.modules.permission.entity.dos.Menu;
import com.wteam.framework.modules.permission.entity.dos.Role;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 管理员VO
 *
 * <AUTHOR>
 * @since 2020-11-22 09:17
 */
@Data
public class AdminUserVO extends AdminUser {

    private static final long serialVersionUID = -2378384199695839312L;

    @Schema(description = "所属部门名称")
    private String departmentTitle;

    @Schema(description = "用户拥有角色")
    private List<Role> roles;

    @Schema(description = "用户拥有的权限")
    private List<Menu> menus;


    public AdminUserVO(AdminUser user) {
        BeanUtil.copyProperties(user, this);
    }

}
