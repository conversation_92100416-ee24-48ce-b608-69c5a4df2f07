package com.wteam.framework.modules.card.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wteam.framework.common.enums.CardTypeEnum;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.book.entity.Card;
import com.wteam.framework.modules.book.entity.Store;
import com.wteam.framework.modules.book.mapper.StoreMapper;
import com.wteam.framework.modules.book.service.StoreService;
import com.wteam.framework.modules.card.entity.CbCardAdmin;
import com.wteam.framework.modules.card.mapper.CardAdminMapper;
import com.wteam.framework.modules.card.service.CardAdminService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/7 22:38
 */
@Slf4j
@Service
public class CardAdminServiceImpl extends ServiceImpl<CardAdminMapper, CbCardAdmin> implements CardAdminService {


    @Autowired
    private CardAdminMapper cardAdminMapper;

    /**
     * 根据传入的卡类型不同
     * 展示对应的卡
     *
     * @param cardTypeEnum
     * @return
     */
    @Override
    public ResultMessage<List<CbCardAdmin>> showAllCard(@NotNull CardTypeEnum cardTypeEnum) {
        // 使用 LambdaQueryWrapper 进行查询
        LambdaQueryWrapper<CbCardAdmin> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CbCardAdmin::getCardType, cardTypeEnum);
        List<CbCardAdmin> cbCardAdmins = cardAdminMapper.selectList(lambdaQueryWrapper);
        log.info("此时从管理员设置的卡信息为：{}", cbCardAdmins);
        if (ObjectUtil.isNull(cbCardAdmins)) {
            return ResultUtil.success();
        }
        return ResultUtil.data(cbCardAdmins);
    }
}
