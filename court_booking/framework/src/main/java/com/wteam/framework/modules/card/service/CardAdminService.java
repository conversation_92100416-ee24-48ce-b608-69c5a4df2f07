package com.wteam.framework.modules.card.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wteam.framework.common.enums.CardTypeEnum;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.book.entity.Card;
import com.wteam.framework.modules.card.entity.CbCardAdmin;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/7 22:37
 */
public interface CardAdminService extends IService<CbCardAdmin> {
    ResultMessage<List<CbCardAdmin>> showAllCard(CardTypeEnum cardTypeEnum);
}
