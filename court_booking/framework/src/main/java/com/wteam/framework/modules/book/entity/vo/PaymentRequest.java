package com.wteam.framework.modules.book.entity.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentRequest {
    private double amount;
    private String extParam;
    private String mchOrderNo;
    private String subject;
    private String wayCode;
    private String sign;
    private String reqTime;
    private String body;
    private String version;
    private String channelExtra;
    private String appId;
    private String clientIp;
    private String notifyUrl;
    private String signType;
    private String currency;
    private String returnUrl;
    private String mchNo;
    private int divisionMode;
}
