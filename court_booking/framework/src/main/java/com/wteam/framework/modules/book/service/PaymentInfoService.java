package com.wteam.framework.modules.book.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.book.entity.PaymentInfo;


/**
 * (PaymentInfo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-20 19:20:06
 */
public interface PaymentInfoService extends IService<PaymentInfo> {

    void wxpay(Long id);

    ResultMessage refund(Long id);

    ResultMessage cardPay(String accessToken, Long userId, Long orderId);
}
