package com.wteam.framework.modules.ocean.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@Data
@TableName("doc")
@Schema(description = "")
public class Doc extends BaseEntity {

	@Schema(description = "")
	private String id;
	@Schema(description = "电子书id")
	private String ebookId;
	@Schema(description = "父id")
	private String parent;
	@Schema(description = "名称")
	private String name;
	@Schema(description = "顺序")
	private Integer sort;
	@Schema(description = "阅读数")
	private Integer viewCount;
	@Schema(description = "点赞数")
	private Integer voteCount;
}