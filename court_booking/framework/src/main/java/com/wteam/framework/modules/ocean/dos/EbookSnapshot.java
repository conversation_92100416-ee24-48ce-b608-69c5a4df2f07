package com.wteam.framework.modules.ocean.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@Data
@TableName("ebook_snapshot")
@Schema(description = "")
public class EbookSnapshot extends BaseEntity {

	@Schema(description = "")
	private String id;
	@Schema(description = "电子书id")
	private String ebookId;
	@Schema(description = "快照日期")
	private Date date;
	@Schema(description = "阅读数")
	private Integer viewCount;
	@Schema(description = "点赞数")
	private Integer voteCount;
	@Schema(description = "阅读增长")
	private Integer viewIncrease;
	@Schema(description = "点赞增长")
	private Integer voteIncrease;
}