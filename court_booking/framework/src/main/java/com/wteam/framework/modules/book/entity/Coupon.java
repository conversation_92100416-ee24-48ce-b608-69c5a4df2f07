package com.wteam.framework.modules.book.entity;


import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (Coupon)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:38:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_coupon")
public class Coupon {
    @TableId
    private Long id;
    //创建者
    private String createBy;
    //创建时间
    private String createTime;
    //删除标志 true/false 删除/未删除
    private Boolean deleteFlag;
    //更新者
    private String updateBy;
    //更新时间
    private String updateTime;
    //优惠天数
    private Integer days;
    //邀请人数
    private Integer numberInvitees;
    //是否可叠加
    private Integer overlay;
    //兑换码
    private String code;
    //生效日期
    private String startDate;
    //结束日期
    private String endDate;
    //使用次数限制
    private String usageLimit;
    //是否有效（0、1）
    private Boolean isActive;
    //优惠卷使用次数
    private String usageCount;
    //折扣类型
    private String discountType;
    //折扣值
    private String discountValue;


}
