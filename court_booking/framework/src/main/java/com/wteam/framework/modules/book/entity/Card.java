package com.wteam.framework.modules.book.entity;

import java.util.Date;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * (Card)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:37:49
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_card")
public class Card {
    @TableId
    private Integer id;
    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //修改者
    private String updateBy;
    //修改时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //卡名称
    private String cardName;
    //卡类型（储值卡、月卡、次卡枚举）
    private String cardType;
    //有效期
    private Integer days;
    //生效日期
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "卡有效开始时间")
    private Date startDate;
    //结束日期
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "卡有效截至时间")
    private Date endDate;
    //面值
    private Integer faceValue;
    //赠送金额
    private Integer bonusAmount;
    //当前余额
    private Double currentBalance;
    //用户id
    private String userId;
    //对应订单id
    //TODO ：把原先的适用门店id改为此卡购买时的订单id
    private Long storeId;
    //折扣值
    private Float discountValue;
    //TODO ：新增次卡使用次数
    private Integer times;
    //订单id
    private String orderId;
}
