package com.wteam.framework.modules.pay.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import com.egzosn.pay.common.bean.CurType;
import com.egzosn.pay.common.bean.OrderParaStructure;
import com.egzosn.pay.common.bean.RefundOrder;
import com.egzosn.pay.common.bean.RefundResult;
import com.egzosn.pay.common.util.Util;
import com.egzosn.pay.wx.v3.bean.WxTransactionType;
import com.egzosn.pay.wx.v3.bean.order.RefundAmount;
import com.egzosn.pay.wx.v3.bean.response.WxRefundResult;
import com.github.binarywang.wxpay.bean.request.WxPayRefundRequest;
import com.github.binarywang.wxpay.bean.result.WxPayRefundResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.exception.ServiceException;
import com.wteam.framework.modules.book.entity.OrderInfo;
import com.wteam.framework.modules.book.mapper.OrderInfoMapper;
import com.wteam.framework.modules.book.service.OrderInfoService;
import com.wteam.framework.modules.pay.entity.enums.PaymentMethodEnum;
import com.wteam.framework.modules.system.service.SettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Map;

import static com.egzosn.pay.wx.api.WxConst.OUT_TRADE_NO;

/**
 * <AUTHOR>
 * @date 2023/10/10 20:14
 */

@Component
@Slf4j
@Service
public class RefundSupport {

    @Autowired
    private SettingService settingService;

    @Autowired
    private OrderInfoService orderInfoService;
}
//    @Autowired
//    private OrderInfoMapper orderInfoMapper
//    @Autowired
//    private WxV3PayPlugin wxV3PayPlugin;

//
//    /**
//     * 退款
//     *
//     * @param paymentMethodEnum
//     * @param sn
//     * @return
//     */
//    @Transactional
//    public Object refund(PaymentMethodEnum paymentMethodEnum, String sn) {
//        if (paymentMethodEnum == null) {
//            throw new ServiceException(ResultCode.PAY_NOT_SUPPORT);
//        }
//        //获取订单信息
//        OrderInfo orderInfo = orderInfoMapper.selectById(sn);
//        if (ObjectUtil.isNull(orderInfo)) {
//            return ResultUtil.error(ResultCode.ENTITY_NULL);
//        }
//
//        //参数需要订单号和退款金额
//        Double orderPrice = orderInfo.getOrderPrice();
//        BigDecimal orderPriceBigDecimal = new BigDecimal(orderPrice);
//        String refundNo = "refund_" + sn;
//
//        //封装退款参数
//        RefundOrder refundOrder = new RefundOrder(refundNo, sn, orderPriceBigDecimal);
//        log.info("此时封装好的退款参数为：{}", refundOrder);
//
//        // 调用退款接口
//        return wxV3PayPlugin.refund(refundOrder);
//    }
//}


//    // 微信退款
//    WxPayRefundRequest wxPayRefundRequest = new WxPayRefundRequest();
//        wxPayRefundRequest.setOutTradeNo(sn);
//        wxPayRefundRequest.setOutRefundNo("refund_" + sn);
//
//    // 元转成分
//    double v = orderInfo.getOrderPrice() * 100;
//    Integer round = Math.toIntExact(Math.round(v));
//        wxPayRefundRequest.setTotalFee(round);
//        wxPayRefundRequest.setRefundFee(round);
//
//    WxPayRefundResult wxPayRefundResult = null;
//        log.info("这里的退款参数是：{}", wxPayRefundResult);
//        try {
//        wxPayRefundResult = wxPayService.refund(wxPayRefundRequest);
//    } catch (WxPayException e) {
//        e.printStackTrace();
//        return ResultUtil.error(ResultCode.ORDER_REFUND_FAILED);
//    }
//        if (!wxPayRefundResult.getResultCode().equals("SUCCESS")) {
//        log.info("refund fail:" + wxPayRefundResult.getReturnMsg());
//        return ResultUtil.error(ResultCode.ORDER_REFUND_FAILED);
//    }
//        return wxPayRefundResult;
//}