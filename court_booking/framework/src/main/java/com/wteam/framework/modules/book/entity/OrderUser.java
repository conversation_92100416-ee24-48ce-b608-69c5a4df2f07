package com.wteam.framework.modules.book.entity;

import java.util.Date;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (OrderUser)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:38:53
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_order_user")
public class OrderUser {

    @TableId
    private Integer id;
    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //修改者
    private String updateBy;
    //修改时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //订单id
    private String orderId;
    //用户id
    private String userId;
    //微信支付订单号
    private String transactionId;
    //订单价格
    private Double orderPrice;
}
