package com.wteam.framework.modules.book.entity;


import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (RoleMenu)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:39:32
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_role_menu")
public class RoleMenu {
    //ID@TableId
    private Long id;

    //创建者
    private String createBy;
    //创建时间
    private String createTime;
    //删除标志 true/false 删除/未删除
    private Boolean deleteFlag;
    //更新者
    private String updateBy;
    //更新时间
    private String updateTime;
    //是否拥有操作数据权限
    private Boolean isSuper;
    //菜单
    private String menuId;
    //角色ID
    private String roleId;


}
