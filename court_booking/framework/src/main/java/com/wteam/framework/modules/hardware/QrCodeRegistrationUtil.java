package com.wteam.framework.modules.hardware;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import com.wteam.framework.common.exception.ServiceException;
import com.wteam.framework.modules.book.entity.Card;
import com.wteam.framework.modules.book.entity.OrderInfo;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
public class QrCodeRegistrationUtil {


    /**
     * @param data
     * @return
     */
    public static Boolean registerTimerQrCode(String data, long StTime, long EdTime) {
        String apiUrl = "https://xiaofeng.pankzone.com/api/openapi/reg_access_qcode.x";
        String mac1 = "863569068849199";
        String mac2 = "863569068848969";
        String tk = ApiCallerUtil.callApi(mac1);
        log.info("此时获取到的token为：{} ", tk);

        String macParam = mac1 + "_" + mac2;
        log.info("此时拼接的macParam为：{}", macParam);

        HttpResponse response = HttpRequest.post(apiUrl)
                .form("mac", macParam)
                .form("tk", tk)
                .form("qcodetxt", data)
                .form("starttime", StTime)
                .form("enabletime", EdTime)
                .form("enablecount", 2)
                .execute();

        String body = response.body();
        JSONObject json = new JSONObject(body);
        int code = json.getInt("code");
        log.info("此时返回的code为：{}", code);
        String qcodetxt = json.getStr("qcodetxt");
        log.info("此时返回的qcodetxt为：{}", qcodetxt);
        String desc = json.getStr("desc");
        log.info("此时返回的desc为：{}", desc);
        if (code != 0 && code != 4) {
            throw new ServiceException("硬件回调url设置接口异常！");
        }
        log.info("此时接口响应数据为：{} (不要记录敏感信息在日志中)", body);
        return true;
    }


    /**
     * @param data
     * @return
     */
    public static Boolean registerMonthQrCode(String data, long StTimestamp, long EdTimestamp) {
        String apiUrl = "https://xiaofeng.pankzone.com/api/openapi/reg_access_qcode.x";
        String mac1 = "863569068849199";
        String mac2 = "863569068848969";
        String tk = ApiCallerUtil.callApi(mac1);
        log.info("此时获取到的token为：{} ", tk);

        String macParam = mac1 + "_" + mac2;
        log.info("此时拼接的macParam为：{}", macParam);

        HttpResponse response = HttpRequest.post(apiUrl).form("mac", macParam)
                .form("tk", tk)
                .form("qcodetxt", data)
                .form("starttime", StTimestamp)
                .form("enabletime", EdTimestamp).form("enablecount", 100).execute();

        String body = response.body();
        JSONObject json = new JSONObject(body);
        int code = json.getInt("code");
        log.info("此时返回的code为：{}", code);
        String qcodetxt = json.getStr("qcodetxt");
        log.info("此时返回的qcodetxt为：{}", qcodetxt);
        String desc = json.getStr("desc");
        log.info("此时返回的desc为：{}", desc);
        if (code != 0 && code != 4) {
            throw new ServiceException("硬件回调url设置接口异常！");
        }
        log.info("此时接口响应数据为：{} (不要记录敏感信息在日志中)", body);
        return true;
    }

    // todo：这里的订单状态次卡可能会有问题
    public static Boolean registerTicketQrCode(String data, Integer times, Card card) {

        Integer realTime = times;
        realTime = realTime * 2;
        log.info("该次卡的使用次数为：{}", realTime);
        String apiUrl = "https://xiaofeng.pankzone.com/api/openapi/reg_access_qcode.x";
        String mac1 = "863569068849199";
        String mac2 = "863569068848969";
        String tk = ApiCallerUtil.callApi(mac1);
        log.info("此时获取到的token为：{} ", tk);


        Date startTime = card.getStartDate();
        Date endTime = card.getEndDate();

        long StTimestamp = startTime.getTime() / 1000;
        log.info("该次卡的开始时间戳为：{}", StTimestamp);
        long EdTimestamp = endTime.getTime() / 1000;
        log.info("该次卡的结束时间戳为：{}", EdTimestamp);

        String macParam = mac1 + "_" + mac2;
        log.info("此时拼接的macParam为：{}", macParam);

        HttpResponse response = HttpRequest.post(apiUrl)
                .form("mac", macParam)
                .form("tk", tk)
                .form("qcodetxt", data)
                .form("starttime", StTimestamp)
                .form("enabletime", EdTimestamp)
                .form("enablecount", realTime).execute();

        String body = response.body();
        JSONObject json = new JSONObject(body);
        int code = json.getInt("code");
        log.info("此时返回的code为：{}", code);
        String qcodetxt = json.getStr("qcodetxt");
        log.info("此时返回的qcodetxt为：{}", qcodetxt);
        String desc = json.getStr("desc");
        log.info("此时返回的desc为：{}", desc);
        if (code != 0 && code != 4) {
            throw new ServiceException("硬件回调url设置接口异常！");
        }
        log.info("此时接口响应数据为：{} (不要记录敏感信息在日志中)", body);
        return true;
    }


    /**
     * 这里生成的二维码是五分钟内有效
     *
     * @param data
     * @return
     */
    public static String registerOneQrCode(String data) {
        String apiUrl = "https://xiaofeng.pankzone.com/api/openapi/reg_access_qcode.x";
        String mac1 = "863569068849199";
        String mac2 = "863569068848969";
        String tk = ApiCallerUtil.callApi(mac1);
        log.info("此时获取到的token为：{} ", tk);

        String macParam = mac1 + "_" + mac2;
        log.info("此时拼接的macParam为：{}", macParam);

        long StTimestamp = System.currentTimeMillis() / 1000;

        long EdTimestamp = StTimestamp + 60 * 5;

        HttpResponse response = HttpRequest.post(apiUrl)
                .form("mac", macParam)
                .form("tk", tk)
                .form("qcodetxt", data)
                .form("starttime", StTimestamp)
                .form("enabletime", EdTimestamp)
                .form("enablecount", 30)
                .execute();

        String body = response.body();
        JSONObject json = new JSONObject(body);
        int code = json.getInt("code");
        log.info("此时返回的code为：{}", code);
        String qcodetxt = json.getStr("qcodetxt");
        log.info("此时返回的qcodetxt为：{}", qcodetxt);
        String desc = json.getStr("desc");
        log.info("此时返回的desc为：{}", desc);
        if (code != 0 && code != 4) {
            throw new ServiceException("硬件回调url设置接口异常！");
        }
        log.info("此时接口响应数据为：{} (不要记录敏感信息在日志中)", body);
        return data;
    }


    public static String registerFiveQrCode(String data) {
        String apiUrl = "https://xiaofeng.pankzone.com/api/openapi/reg_access_qcode.x";
        String mac1 = "863569068849199";
        String mac2 = "863569068848969";
        String tk = ApiCallerUtil.callApi(mac1);
        log.info("此时获取到的token为：{} ", tk);

        String macParam = mac1 + "_" + mac2;
        log.info("此时拼接的macParam为：{}", macParam);

        long StTimestamp = System.currentTimeMillis() / 1000;

        long EdTimestamp = StTimestamp + 60 * 5;

        HttpResponse response = HttpRequest.post(apiUrl)
                .form("mac", macParam)
                .form("tk", tk)
                .form("qcodetxt", data)
                .form("starttime", StTimestamp)
                .form("enabletime", EdTimestamp)
                .form("enablecount", 1)
                .execute();

        String body = response.body();
        JSONObject json = new JSONObject(body);
        int code = json.getInt("code");
        log.info("此时返回的code为：{}", code);
        String qcodetxt = json.getStr("qcodetxt");
        log.info("此时返回的qcodetxt为：{}", qcodetxt);
        String desc = json.getStr("desc");
        log.info("此时返回的desc为：{}", desc);
        if (code != 0 && code != 4) {
            throw new ServiceException("硬件回调url设置接口异常！");
        }
        log.info("此时接口响应数据为：{} (不要记录敏感信息在日志中)", body);
        return data;
    }

    /**
     * 这个方法是用来注册计时码的 此刻生成的码有效期是24小时 核销次数为2次
     *
     * @param orderInfo
     * @return
     */
    public static String registerCode(OrderInfo orderInfo) {
        // 获取当前日期
        LocalDate currentDate1 = LocalDate.now();
        // 格式化日期为字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedDate = currentDate1.format(formatter);

        //生成一个随机数字
        String numbers = RandomUtil.randomNumbers(5);

        String data = "pxo_" + formattedDate + "_" + numbers + "_" + orderInfo.getId() + "_00";

        //获得当前时间戳
        long StTime = System.currentTimeMillis() / 1000;
        //获得24之后的时间戳
        long EdTime = StTime + 1000 * 60 * 24 * 60;

        Boolean b = QrCodeRegistrationUtil.registerTimerQrCode(data, StTime, EdTime);

        if (b) {
            return data;
        }
        return "注册二维码错误！";
    }
}
