package com.wteam.framework.modules.pay.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 支付参数
 *
 * <AUTHOR>
 * @since 2020/12/19 11:46
 */
@Data
@ToString
public class PayParam {


    @NotNull
    @ApiModelProperty(value = "支付方式", allowableValues = "ALIPAY,WECHAT,UNIONPAY")
    private String orderType;

    @NotNull
    @ApiModelProperty(value = "订单号")
    private String sn;

    @ApiModelProperty(value = "客户端类型")
    private String clientType;


}
