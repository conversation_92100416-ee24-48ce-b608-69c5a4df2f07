package com.wteam.framework.modules.ocean.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
@Data
@TableName("user")
@Schema(description = "")
public class User extends BaseEntity {

	@Schema(description = "ID")
	private String id;
	@Schema(description = "登录名")
	private String loginName;
	@Schema(description = "昵称")
	private String name;
	@Schema(description = "密码")
	private String password;
}