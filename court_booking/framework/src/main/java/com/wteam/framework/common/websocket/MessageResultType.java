package com.wteam.framework.common.websocket;

/**
 * 返回消息类型枚举
 *
 * <AUTHOR>
 */
public enum MessageResultType {
    /**
     * 返回消息类型枚举
     * <p>
     * 好友列表
     * 增加好友
     * 消息
     * 阅读消息
     * 未读消息
     * 历史消息
     * 系统提示
     * 下线提醒
     */
    FRIENDS,
    ADD_FRIENDS,
    MESSAGE,
    READ_MESSAGE,
    UN_READ,
    HISTORY,
    SYSTEM_TIPS,
    OFFLINE,
    SERVER,
    SAMEMONTH,
    SAMEDAY,
    DEVICECOUNT;

}
