package com.wteam.framework.modules.book.entity.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * <AUTHOR>
 * @date 2023/9/20 12:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class OrderReq {
    //场地id
    private Long venueId;
    //门店id
    private Long storeId;
    //门店名称
    private String storeName;
    //门店地址
    private String address;
    //预约日期
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date orderDate;
    //预约开始时间
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "预约开始时间")
    private Date orderSt;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "预约结束时间")
    private Date orderEd;
    //订单类型（枚举）
    private String orderType;
    //手机号码
    private String phoneNumber;
    //订单价格
    private Double orderPrice;
    //优惠卷id
    private Long id;
    //场地关联id
    private String relevancyId;

}
