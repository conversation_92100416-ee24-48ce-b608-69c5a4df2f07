package com.wteam.framework.modules.book.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.websocket.OnOpen;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 16:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StorePhotoDto {
    /**
     * 门店id
     * 该门店对应的图片信息
     */
    private Long store_id;
    private List<String> images;
}
