package com.wteam.framework.modules.specialValue.entity;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableName;
/**
 * (CbSpecialValue)表实体类
 *
 * <AUTHOR>
 * @since 2023-10-01 14:17:38
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_special_value")
public class CbSpecialValue  {

    //特殊价格的id
    private Integer id;
    //场地id
    private Integer venueId;
    //开门时间
    private Date orderst;
    //关门时间
    private Date ordered;
    //后台指定特殊价格
    private Integer specialValue;



}
