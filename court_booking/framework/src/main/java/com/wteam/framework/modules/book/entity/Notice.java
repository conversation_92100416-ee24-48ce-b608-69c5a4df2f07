package com.wteam.framework.modules.book.entity;


import java.util.Date;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (Notice)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:38:38
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_notice")
public class Notice {
    //id@TableId
    private Integer id;

    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //修改者
    private String updateBy;
    //修改时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //通知日期
    private Date noticeTime;
    //通知人
    private String noticePerson;
    //通知内容
    private String noticeContent;
    //通知标题
    private String noticeTitle;
    //通知状态 为0代表通知未被删除,1代表通知被删除了
    private Boolean noticeState;
    //门店id
    private Long storeId;


}
