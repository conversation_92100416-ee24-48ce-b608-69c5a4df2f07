package com.wteam.framework.modules.ocean.dos.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wteam.framework.common.vo.PageVO;
import com.wteam.framework.modules.ocean.dos.Category;

/**
 *
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-06-22
 */
public interface CategoryService extends IService<Category> {

    /**
     * 根据名称模糊查询分类
     *
     * @param name 分类名称（支持模糊查询）
     * @param pageVO 分页参数
     * @return 分页结果
     */
    IPage<Category> searchByName(String name, PageVO pageVO);

}