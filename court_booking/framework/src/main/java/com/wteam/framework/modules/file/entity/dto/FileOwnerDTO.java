package com.wteam.framework.modules.file.entity.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件查询所属者参数对象
 *
 * <AUTHOR>
 * @since 2021-02-22 17:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileOwnerDTO {

    @Schema(description = "拥有者id")
    private String ownerId;

    @Schema(description = "用户类型")
    private String userEnums;

}