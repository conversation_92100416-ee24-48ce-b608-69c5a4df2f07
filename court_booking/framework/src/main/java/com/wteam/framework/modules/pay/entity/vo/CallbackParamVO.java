package com.wteam.framework.modules.pay.entity.vo;


import com.wteam.framework.modules.pay.entity.enums.PayStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> lsllsl
 * @version : [v1.0]
 * @className : CallbackParamVO
 * @description : [描述说明该类的功能]
 * @createTime : [2023/4/12 21:26]
 * @updateUser : [LiuYanQiang]
 * @updateTime : [2023/4/12 21:26]
 * @updateRemark : [描述说明本次修改内容]
 */
@Data
@NoArgsConstructor
public class CallbackParamVO {
    @ApiModelProperty(value = "外部支付订单号")
    private String transactionId;

    @ApiModelProperty(value = "交易状态")
    private String tradeState;

    @ApiModelProperty(value = "内部订单号")
    private String sn;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmount;

    @ApiModelProperty(value = "支付类型")
    private String payType;

    @ApiModelProperty(value = "订单支付时间")
    private Date successTime;

    public CallbackParamVO(String transactionId, String sn, String payType, BigDecimal payAmount, Date successTime) {
        this.transactionId = transactionId;
        this.sn = sn;
        this.payAmount = payAmount;
        this.payType = payType;
        this.tradeState = PayStatusEnum.PAID.name();
        this.successTime = successTime;
    }

}
