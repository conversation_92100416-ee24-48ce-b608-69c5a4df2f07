package com.wteam.framework.modules.member.entity.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 会员搜索VO
 *
 * <AUTHOR>
 * @since 2020/12/15 10:48
 */
@Data
public class MemberSearchVO {

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "昵称")
    private String nickName;

    @Schema(description = "用户手机号码")
    private String mobile;

    /**
     * //     * @see SwitchEnum
     */
    @Schema(description = "会员状态")
    private String disabled;
}
