package com.wteam.framework.modules.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wteam.framework.common.enums.CardTypeEnum;
import com.wteam.framework.common.enums.OrderTypeEnums;
import com.wteam.framework.common.enums.orderTypeEnum;
import com.wteam.framework.common.security.enums.UserEnums;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.book.entity.OrderInfo;
import com.wteam.framework.modules.book.entity.vo.OrderInfoVo;
import com.wteam.framework.modules.user.entity.User;
import com.wteam.framework.modules.user.entity.dto.WechatMPLoginParams;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.List;
import java.util.Map;


/**
 * (User)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-14 19:28:44
 */
@Service
public interface UserService extends IService<User> {

    /**
     * 微信授权
     *
     * @param params
     * @return
     */
    Map<String, Object> miniProgramLogin(WechatMPLoginParams params);


    /**
     * 获取user
     *
     * @param
     * @return
     */
    User getUserInfo();

    /**
     * 获取手机号
     *
     * @param params
     */

    void getPhoneInfo(WechatMPLoginParams params);


    /**
     * 退出登录
     */
    ResultMessage exit(HttpServletRequest request);


    /**
     * 登出
     *
     * @param userEnums token角色类型
     */
    void logout(UserEnums userEnums);

    ResultMessage showOrderType(String accessToken, orderTypeEnum orderTypeEnum);

    ResultMessage showMyCard(Long id, CardTypeEnum cardTypeEnum);

    ResultMessage showMyCoupon(String accessToken);

    ResultMessage showOrder(String id);

    ResultMessage showOrderByType(String id, OrderTypeEnums orderTypeEnum) throws NullPointerException;
}
