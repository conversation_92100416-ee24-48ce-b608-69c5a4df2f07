package com.wteam.framework.modules.system.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;

import com.wteam.framework.common.mybatis.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 设置
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("cb_setting")
@Schema(description = "配置")
@NoArgsConstructor
public class Setting extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配置值value")
    private String settingValue;

}