package com.wteam.framework.modules.card.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;


import java.util.Date;

@Data
@TableName("cb_card_admin")
@Schema(description = "管理员储值卡表")
public class CbCardAdmin {

    @Schema(description = "id")
    private Integer id;
    @Schema(description = "卡名称")
    private String cardName;
    @Schema(description = "卡类型（储值卡，月卡，次卡，散客储值卡枚举）")
    private String cardType;
    @Schema(description = "有效期")
    private Integer days;
    @Schema(description = "面值")
    private Integer faceValue;
    @Schema(description = "赠送金额")
    private Integer bonusAmount;
    @Schema(description = "折扣值")
    private Float discountValue;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "次卡次数")
    private Integer times;
    @Schema(description = "详情简介")
    private String detailedIntroduction;
    @Schema(description = "使用说明")
    private String usageInstructions;
    @Schema(description = "生效日期")
    private String startDate;
    @Schema(description = "结束日期")
    private String endDate;
}