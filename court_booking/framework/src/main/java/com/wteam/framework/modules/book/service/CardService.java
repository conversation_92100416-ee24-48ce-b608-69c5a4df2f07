package com.wteam.framework.modules.book.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jeequan.jeepay.exception.JeepayException;
import com.wteam.framework.common.enums.CardTypeEnum;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.book.entity.Card;
import com.wteam.framework.modules.book.entity.dto.OrderCardReq;
import org.springframework.stereotype.Service;


/**
 * (Card)表服务接口
 * <AUTHOR>
 * @since 2023-09-18 19:19:57
 */
@Service
public interface CardService extends IService<Card> {

    ResultMessage showCard(Long id, CardTypeEnum cardTypeEnum);

    ResultMessage showCardDetails(Long id);

    ResultMessage buyCard(String accessToken, String id, Long cardId);

    ResultMessage chargeCard(String accessToken, Long id);

    ResultMessage generateCardOrder(String accessToken, OrderCardReq orderCardReq);

    ResultMessage valueCardPay(String orderId, Integer cardId);

    ResultMessage getQrCodeByCard(String cardId);

    ResultMessage cardCharge(String orderId, Integer cardId);

    ResultMessage getChargeCardOrder(String accessToken, Integer cardId,Double currentBalance);

    ResultMessage personCardPay(String orderId, Integer cardId);
}
