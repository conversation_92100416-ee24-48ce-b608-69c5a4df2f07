package com.wteam.framework.modules.permission.entity.dos;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wteam.framework.common.mybatis.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * 部门
 *
 * <AUTHOR>
 * @since 2020/11/19 11:57
 */
@Data
@TableName("li_department")
@Schema(description = "部门")
@EqualsAndHashCode(callSuper = true)
public class Department extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门名称")
    @NotNull(message = "部门名称不能为空")
    private String title;

    @Schema(description = "父id")
    private String parentId;

    @Schema(description = "排序值")
    private Double sortOrder;
}