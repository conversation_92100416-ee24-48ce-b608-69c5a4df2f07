package com.wteam.framework.modules.book.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/6 14:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderTicketVo {
    @TableId
    private String id;

    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //修改者
    private String updateBy;
    //修改时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //用户id
    private String userId;
    //门店id
    private Long storeId;
    //预约日期
    @Schema(description = "预约日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date orderDate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "预约开始时间")
    private Date orderSt;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "预约结束时间")
    private Date orderEd;
    //订单状态(枚举）
    private String orderState;
    //订单价格
    private Double orderPrice;
    //订单类型（枚举）
    private String orderType;
    //支付方式
    private String paymentMethods;
    //分享次数
    private Integer share;
    //场地id
    private Long venueId;
    //手机号码
    private String phoneNumber;
    //二维码
    private String qrCode;
    //用户进场时间
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "用户进场时间")
    private Date startTime;
    //用户离开时间
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "用户离场时间")
    private Date endTime;
    //购买卡的id
    private Integer cardId;
    //不知名id
    private Long transactionId;
    //订单超时金额
    private Double overPrice;
    //有效期
    private Integer days;
    //生效日期
    private String startDate;
    //结束日期
    private String endDate;

}
