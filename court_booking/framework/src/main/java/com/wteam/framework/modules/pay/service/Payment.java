package com.wteam.framework.modules.pay.service;


import com.egzosn.pay.common.bean.RefundResult;
import com.wteam.framework.common.enums.ResultCode;
import com.wteam.framework.common.exception.ServiceException;
import com.wteam.framework.common.vo.ResultMessage;

import com.wteam.framework.modules.pay.entity.dto.PayParam;
import com.wteam.framework.modules.pay.entity.enums.PaymentMethodEnum;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Map;

/**
 * 支付接口
 *
 * <AUTHOR>
 * @since 2020-12-21 09:32
 */
@Service
public interface Payment {


    /**
     * 退款
     *
     * @param paymentMethodEnum
     * @param sn
     * @return
     */
    default RefundResult refund(PaymentMethodEnum paymentMethodEnum, String sn) throws ParseException {
        throw new ServiceException(ResultCode.PAY_ERROR);
    }


    /**
     * 后台管理员退款
     *
     * @param paymentMethodEnum
     * @param sn
     * @return
     */
    default RefundResult adminRefund(PaymentMethodEnum paymentMethodEnum, String sn) throws ParseException {
        throw new ServiceException(ResultCode.PAY_ERROR);
    }

    /**
     * 回调
     *
     * @param request HttpServletRequest
     */
    default String callBack(HttpServletRequest request) {
        throw new ServiceException(ResultCode.PAY_ERROR);
    }

    /**
     * 异步通知
     *
     * @param request HttpServletRequest
     */
    default void notify(HttpServletRequest request) {
        throw new ServiceException(ResultCode.PAY_ERROR);
    }

    default Map<String, Object> query(PayParam payParam) {
        throw new ServiceException(ResultCode.PAY_ERROR);
    }

    ;

    /**
     * 小程序调起支付
     *
     * @param request HttpServletRequest
     * @param sn      支付订单号
     * @return 路径
     */
    default ResultMessage<Object> jsapiPay(HttpServletRequest request, String sn, PaymentMethodEnum paymentMethodEnum) {
        throw new ServiceException(ResultCode.PAY_ERROR);
    }

}
