package com.wteam.framework.modules.book.entity;

import java.util.Date;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (PaymentInfo)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:39:04
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_payment_info")
public class PaymentInfo {
    //支付记录id@TableId
    private String id;

    //创建者
    private String createBy;
    //创建时间
    private Date createTime;
    //修改者
    private String updateBy;
    //更新时间
    private Date updateTime;
    //删除标志 true/false 删除/未删除
    private Integer deleteFlag;
    //支付类型
    private String paymentType;
    //交易类型（这里可能会有退款）
    private String tradeType;
    //交易状态
    private String tradeState;
    //支付金额(分)
    private Integer payerTotal;
    //通知参数
    private String content;
    //商户订单编号id
    private String orderId;
    //支付系统交易编号
    private String transactionId;
    //支付金额
    private Double orderPrice;
    //时间
    private Date time;


}
