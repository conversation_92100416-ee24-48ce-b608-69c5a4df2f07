package com.wteam.framework.modules.book.entity.reqp;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneOffset;

public class TimeCalculation {
    public static void main(String[] args) {
        // 假设您有一个订单的开始时间和结束时间
        LocalTime orderSt = LocalTime.of(10, 0); // 假设订单开始时间是10:00 AM
        LocalTime orderEd = LocalTime.of(12, 30); // 假设订单结束时间是12:30 PM

        // 计算比orderSt早5分钟
        LocalTime fiveMinutesBeforeOrderSt = orderSt.minusMinutes(5);

        // 计算比orderEd晚15分钟
        LocalTime fifteenMinutesAfterOrderEd = orderEd.plusMinutes(15);

        // 转换为时间戳
        long timestampFiveMinutesBeforeOrderSt = fiveMinutesBeforeOrderSt.atDate(LocalDate.now())
                .toInstant(ZoneOffset.UTC)
                .toEpochMilli();

        long timestampFifteenMinutesAfterOrderEd = fifteenMinutesAfterOrderEd.atDate(LocalDate.now())
                .toInstant(ZoneOffset.UTC)
                .toEpochMilli();

        // 打印结果
        System.out.println("比orderSt早5分钟的时间戳：" + timestampFiveMinutesBeforeOrderSt);
        System.out.println("比orderEd晚15分钟的时间戳：" + timestampFifteenMinutesAfterOrderEd);
    }
}
