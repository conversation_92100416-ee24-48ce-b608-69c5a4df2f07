package com.wteam.framework.modules.book.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/9/23 20:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CardVo {
    //卡名称
    private String cardName;
    //卡类型（储值卡、月卡、次卡枚举）
    private String cardType;
    //有效期
    private Integer days;
    //生效日期
    private String startDate;
    //结束日期
    private String endDate;
    //面值
    private Integer faceValue;
    //赠送金额
    private Integer bonusAmount;
    //当前余额
    private Double currentBalance;
    //TODO ：新增次卡使用次数
    private Integer times;
    @Schema(description = "详情简介")
    private String detailedIntroduction;
    @Schema(description = "使用说明")
    private String usageInstructions;
    //购买该卡对应的订单id
    private String orderId;
}
