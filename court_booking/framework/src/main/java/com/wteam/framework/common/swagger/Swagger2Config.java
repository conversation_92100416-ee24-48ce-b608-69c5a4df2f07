package com.wteam.framework.common.swagger;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.ExternalDocumentation;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
//@EnableSwagger2WebMvc
public class Swagger2Config {

    @Value("${swagger.title}")
    private String title;

    @Value("${swagger.description}")
    private String description;

    @Value("${swagger.version}")
    private String version;

    @Value("${swagger.termsOfServiceUrl}")
    private String termsOfServiceUrl;

    @Value("${swagger.contact.name}")
    private String name;

    @Value("${swagger.contact.url}")
    private String url;

    @Value("${swagger.contact.email}")
    private String email;
    private static final String headerName = "accessToken";//请求头名称
    @Bean
    public OpenAPI defaultOpenAPI() {
        Components components = new Components();
        //添加右上角的统一安全认证
        components.addSecuritySchemes(headerName,
                new SecurityScheme()
                        .type(SecurityScheme.Type.APIKEY)
                        .scheme("basic")
                        .name(headerName)
                        .in(SecurityScheme.In.HEADER)
                        .description("请求头")
        );

        return new OpenAPI()
                .info(info())
                .externalDocs(documentation())
                .addSecurityItem(new SecurityRequirement().addList("Authorization"))
                .components(components);

    }

    public Info info() {
        return new Info()
                .title(title)
                .version(version)
                .description(description)
                .license(new License().name("许可证名称").url("许可证地址"))
                .contact(new Contact().name(name).url(email))
                .summary("概要");
    }
    public ExternalDocumentation documentation() {
        return new ExternalDocumentation().description("文档描述").url("文档地址");
    }



    @Bean
    public GroupedOpenApi authApi() {
        return GroupedOpenApi.builder()
                .displayName("登录")
                .group("user")
                .addOperationCustomizer((operation, handlerMethod) -> {
                    operation.addSecurityItem(new SecurityRequirement().addList(headerName));
                    return operation;
                })
                //这里原先的包是com.wteam.controller.passport
                //client-api包下的controller
                .packagesToScan("com.wteam.controller")
                .build();
    }

    @Bean
    public GroupedOpenApi commonApi() {
        return GroupedOpenApi.builder()
                .displayName("通用")
                .group("common")
                .addOperationCustomizer((operation, handlerMethod) -> {
                    operation.addSecurityItem(new SecurityRequirement().addList(headerName));
                    return operation;
                })
                .packagesToScan("com.wteam.controller.common")
                .build();
    }

    @Bean
    public GroupedOpenApi permissionApi() {
        return GroupedOpenApi.builder()
                .displayName("权限")
                .group("permission")
                .addOperationCustomizer((operation, handlerMethod) -> {
                    operation.addSecurityItem(new SecurityRequirement().addList(headerName));
                    return operation;
                })
                .packagesToScan("com.wteam.controller.permission")
                .build();
    }

    @Bean
    public GroupedOpenApi smsApi() {
        return GroupedOpenApi.builder()
                .displayName("短信")
                .group("sms")
                .addOperationCustomizer((operation, handlerMethod) -> {
                    operation.addSecurityItem(new SecurityRequirement().addList(headerName));
                    return operation;
                })
                .packagesToScan("com.wteam.controller.sms")
                .build();
    }
}
