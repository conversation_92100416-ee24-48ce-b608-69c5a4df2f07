package com.wteam.framework.modules.image.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.image.entity.CbImage;
import com.wteam.framework.modules.image.mapper.CbImageMapper;
import com.wteam.framework.modules.image.service.CbImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisCallback;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (CbImage)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-30 19:32:05
 */
@Service("cbImageService")
public class CbImageServiceImpl extends ServiceImpl<CbImageMapper, CbImage> implements CbImageService {
    @Autowired
    private CbImageMapper cbImageMapper;

    @Override
    public ResultMessage getPhoto() {
        List<CbImage> cbImages = cbImageMapper.selectList(new QueryWrapper<>());

        return ResultUtil.data(cbImages);
    }
}
