package com.wteam.framework.modules.book.entity;


import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * (Role)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-16 20:39:22
 */
@SuppressWarnings("serial")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("cb_role")
public class Role {
    //ID@TableId
    private Long id;

    //创建者
    private String createBy;
    //创建时间
    private String createTime;
    //删除标志 true/false 删除/未删除
    private Boolean deleteFlag;
    //更新者
    private String updateBy;
    //更新时间
    private String updateTime;
    //是否为注册默认角色
    private Boolean defaultRole;
    //备注
    private String description;
    //角色名
    private String name;


}
