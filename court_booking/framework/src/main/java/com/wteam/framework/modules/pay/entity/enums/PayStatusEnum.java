package com.wteam.framework.modules.pay.entity.enums;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @since 2020/11/17 7:28 下午
 */
public enum PayStatusEnum {

    /**
     * 支付状态
     */
    UNPAID("待付款"),
    PAID("已付款"),
    CANCEL("已取消"),
    COMPLETED("已完成");

    private final String description;

    PayStatusEnum(String description) {
        this.description = description;
    }

    public String description() {
        return this.description;
    }


}
