package com.wteam.framework.modules.permission.entity.vo;


import com.wteam.framework.modules.permission.entity.dos.Role;
import com.wteam.framework.modules.permission.entity.dos.RoleMenu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * RoleVO
 *
 * <AUTHOR>
 * @since 2020-11-22 17:42
 */
@Data
public class RoleVO extends Role {

    private static final long serialVersionUID = 8625345346785692513L;

    @Schema(description = "拥有权限")
    private List<RoleMenu> roleMenus;
}
