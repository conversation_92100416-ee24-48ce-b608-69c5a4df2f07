package com.wteam.framework.modules.connect.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 授权回调时的参数类
 *
 * <AUTHOR> (yadong.zhang0415(a)gmail.com)
 * @since 1.8.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthCallback implements Serializable {

    /**
     * 访问AuthorizeUrl后回调时带的参数code
     */
    private String code;

    /**
     * 访问AuthorizeUrl后回调时带的参数auth_code，该参数目前只使用于支付宝登录
     */
    private String authCode;

    /**
     * 访问AuthorizeUrl后回调时带的参数state，用于和请求AuthorizeUrl前的state比较，防止CSRF攻击
     */
    private String state;

}
