package com.wteam.framework.modules.specialValue.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wteam.framework.common.enums.ResultUtil;
import com.wteam.framework.common.vo.ResultMessage;
import com.wteam.framework.modules.book.entity.OrderInfo;
import com.wteam.framework.modules.specialValue.entity.CbSpecialValue;
import com.wteam.framework.modules.specialValue.mapper.CbSpecialValueMapper;
import com.wteam.framework.modules.specialValue.service.CbSpecialValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * (CbSpecialValue)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-01 14:17:39
 */
@Service("cbSpecialValueService")
public class CbSpecialValueServiceImpl extends ServiceImpl<CbSpecialValueMapper, CbSpecialValue> implements CbSpecialValueService {

    @Autowired
    private CbSpecialValueMapper cbSpecialValueMapper;

    @Override
    public ResultMessage getSpecialValue() {
        List<CbSpecialValue> cbSpecialValues = cbSpecialValueMapper.selectList(new QueryWrapper<>());
        //格式化时间
        List<Map<String, Object>> maps = formatOrderInfo(cbSpecialValues);


        if (maps.isEmpty()) {
            return ResultUtil.success();
        }
        return ResultUtil.data(maps);
    }

    public List<Map<String, Object>> formatOrderInfo(List<CbSpecialValue> orderInfos) {
        List<Map<String, Object>> dataList = new ArrayList<>();

        for (CbSpecialValue CbSpecialValues : orderInfos) {
            Map<String, Object> orderMap = new HashMap<>();
            orderMap.put("venueId", CbSpecialValues.getVenueId());
            orderMap.put("orderSt", formatTime(CbSpecialValues.getOrderst()));
            orderMap.put("orderEd", formatTime(CbSpecialValues.getOrdered()));
            orderMap.put("special_value", CbSpecialValues.getSpecialValue());
            dataList.add(orderMap);
        }

        return dataList;
    }

    private String formatTime(Date time) {
        SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return timeFormat.format(time);
    }

}




